<?php
// Script de débogage pour l'API

echo "<h1>Debug API EasyBus</h1>";

// 1. Vérifier la configuration PHP
echo "<h2>1. Configuration PHP</h2>";
echo "Version PHP: " . phpversion() . "<br>";
echo "Extensions chargées: " . implode(", ", get_loaded_extensions()) . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";

// 2. Vérifier les fichiers
echo "<h2>2. Vérification des fichiers</h2>";
$files_to_check = [
    'api/index.php',
    'api/vendor/autoload.php',
    'api/src/Router/Router.php',
    'api/src/Helpers/response.php',
    'api/src/Controllers/TestController.php',
    'api/.env'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $color = $exists ? 'green' : 'red';
    echo "<span style='color: $color'>$file: " . ($exists ? 'EXISTS' : 'MISSING') . "</span><br>";
}

// 3. Tester l'autoloader
echo "<h2>3. Test Autoloader</h2>";
try {
    require_once 'api/vendor/autoload.php';
    echo "<span style='color: green'>Autoloader chargé avec succès</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red'>Erreur autoloader: " . $e->getMessage() . "</span><br>";
}

// 4. Tester les variables d'environnement
echo "<h2>4. Variables d'environnement</h2>";
if (file_exists('api/.env')) {
    try {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/api');
        $dotenv->load();
        echo "<span style='color: green'>Variables d'environnement chargées</span><br>";
        echo "DB_HOST: " . ($_ENV['DB_HOST'] ?? 'NON DÉFINI') . "<br>";
        echo "DB_NAME: " . ($_ENV['DB_NAME'] ?? 'NON DÉFINI') . "<br>";
    } catch (Exception $e) {
        echo "<span style='color: red'>Erreur .env: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: red'>Fichier .env manquant</span><br>";
}

// 5. Tester la base de données
echo "<h2>5. Test Base de données</h2>";
try {
    $pdo = new PDO(
        "mysql:host=" . ($_ENV['DB_HOST'] ?? 'localhost') . ";dbname=" . ($_ENV['DB_NAME'] ?? 'bus_booking'),
        $_ENV['DB_USER'] ?? 'root',
        $_ENV['DB_PASS'] ?? '',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "<span style='color: green'>Connexion base de données réussie</span><br>";
    
    // Test simple
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM locations");
    $result = $stmt->fetch();
    echo "Nombre de locations: " . $result['count'] . "<br>";
    
} catch (Exception $e) {
    echo "<span style='color: red'>Erreur base de données: " . $e->getMessage() . "</span><br>";
}

// 6. Tester le routeur
echo "<h2>6. Test Routeur</h2>";
try {
    require_once 'api/src/Router/Router.php';
    $router = new Router();
    echo "<span style='color: green'>Routeur instancié avec succès</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red'>Erreur routeur: " . $e->getMessage() . "</span><br>";
}

// 7. Tester une requête API directe
echo "<h2>7. Test API Direct</h2>";
echo "<a href='api/v1/test' target='_blank'>Tester /api/v1/test</a><br>";
echo "<a href='api/v1/health' target='_blank'>Tester /api/v1/health</a><br>";
echo "<a href='api/v1/locations' target='_blank'>Tester /api/v1/locations</a><br>";

// 8. Informations sur les headers
echo "<h2>8. Headers de la requête</h2>";
foreach (getallheaders() as $name => $value) {
    echo "$name: $value<br>";
}

// 9. Test de réécriture d'URL
echo "<h2>9. Test réécriture URL</h2>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'NON DÉFINI') . "<br>";

// 10. Logs récents
echo "<h2>10. Logs récents</h2>";
$logFile = 'api/logs/app.log';
if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $recentLogs = array_slice(explode("\n", $logs), -10);
    foreach ($recentLogs as $log) {
        if (!empty($log)) {
            echo htmlspecialchars($log) . "<br>";
        }
    }
} else {
    echo "Aucun fichier de log trouvé<br>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
