<?php
require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../Helpers/response.php';

class Router {
    private $router;

    public function __construct() {
        $this->router = new AltoRouter();
        $this->setupRoutes();
    }
    
    private function setupRoutes() {
        // Détecter automatiquement le basePath
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);

        // Si on est dans un sous-dossier, ajuster le basePath
        if (strpos($_SERVER['REQUEST_URI'], '/bus-booking/') !== false) {
            $basePath = '/bus-booking/api';
        } else {
            $basePath = '/api';
        }

        $this->router->setBasePath($basePath);

        // Route de test
        $this->router->map('GET', '/v1/test', 'TestController#test');
        $this->router->map('GET', '/v1/health', 'TestController#health');

        // Routes d'authentification
        $this->router->map('POST', '/v1/auth/register', 'UserController#register');
        $this->router->map('POST', '/v1/auth/login', 'UserController#login');
        $this->router->map('POST', '/v1/auth/logout', 'UserController#logout');
        $this->router->map('POST', '/v1/auth/refresh', 'UserController#refreshToken');

        // Routes utilisateurs
        $this->router->map('GET', '/v1/users/profile', 'UserController#getProfile');
        $this->router->map('PUT', '/v1/users/profile', 'UserController#updateProfile');
        $this->router->map('GET', '/v1/users/dashboard', 'UserController#getDashboard');

        // Routes de localisation
        $this->router->map('GET', '/v1/locations', 'LocationController#getLocations');
        $this->router->map('GET', '/v1/locations/[i:id]', 'LocationController#getLocationById');

        // Routes des trajets
        $this->router->map('GET', '/v1/routes', 'RouteController#getAllRoutes');
        $this->router->map('GET', '/v1/routes/[i:id]', 'RouteController#getRouteById');
        $this->router->map('POST', '/v1/routes', 'RouteController#createRoute');
        $this->router->map('PUT', '/v1/routes/[i:id]', 'RouteController#updateRoute');
        $this->router->map('DELETE', '/v1/routes/[i:id]', 'RouteController#deleteRoute');

        // Routes des voyages
        $this->router->map('GET', '/v1/trips/search', 'TripController#searchTrips');
        $this->router->map('GET', '/v1/trips/[i:id]', 'TripController#getTripDetails');
        $this->router->map('GET', '/v1/trips/[i:id]/stops', 'TripController#getTripStops');
        $this->router->map('GET', '/v1/trips/[i:id]/seats', 'TripController#getTripSeats');
        $this->router->map('POST', '/v1/trips', 'TripController#createTrip');
        $this->router->map('PUT', '/v1/trips/[i:id]', 'TripController#updateTrip');
        $this->router->map('DELETE', '/v1/trips/[i:id]', 'TripController#cancelTrip');
        
        // Routes des réservations
        $this->router->map('GET', '/v1/bookings', 'BookingController#getUserBookings');
        $this->router->map('GET', '/v1/bookings/[i:id]', 'BookingController#getBookingById');
        $this->router->map('POST', '/v1/bookings', 'BookingController#createBooking');
        $this->router->map('PUT', '/v1/bookings/[i:id]', 'BookingController#updateBooking');
        $this->router->map('DELETE', '/v1/bookings/[i:id]', 'BookingController#cancelBooking');

        $this->router->map('GET', '/v1/bookings/search', 'BookingController#searchBookings');

        // Routes des paiements
        $this->router->map('POST', '/v1/payments', 'PaymentController#createPayment');
        $this->router->map('GET', '/v1/payments/[i:id]', 'PaymentController#getPaymentStatus');
        $this->router->map('POST', '/v1/payments/fedapay/webhook', 'PaymentController#handleFedaPayWebhook');

        // Routes des équipements
        $this->router->map('GET', '/v1/amenities', 'BusController#getAllAmenities');
        
        // Routes des bus (opérateurs)
        $this->router->map('GET', '/v1/buses', 'BusController#getAllBuses');
        $this->router->map('GET', '/v1/buses/[i:id]', 'BusController#getBusById');
        $this->router->map('POST', '/v1/buses', 'BusController#createBus');
        $this->router->map('PUT', '/v1/buses/[i:id]', 'BusController#updateBus');
        $this->router->map('DELETE', '/v1/buses/[i:id]', 'BusController#deleteBus');

        // Routes des tickets
        $this->router->map('GET', '/v1/tickets/[*:code]', 'TicketController#getTicketByCode');
        $this->router->map('POST', '/v1/tickets/validate', 'TicketController#validateTicket');
        $this->router->map('GET', '/v1/tickets/[i:id]/qr', 'TicketController#generateQRCode');
        $this->router->map('GET', '/v1/bookings/[i:booking_id]/tickets', 'TicketController#getBookingTickets');

        // Routes du tableau de bord opérateur
        $this->router->map('GET', '/v1/operator/dashboard', 'OperatorController#getDashboard');
        $this->router->map('GET', '/v1/operator/bookings', 'OperatorController#getBookings');
        $this->router->map('GET', '/v1/operator/reports', 'OperatorController#getReports');
        $this->router->map('GET', '/v1/operator/analytics', 'OperatorController#getAnalytics');

        // Routes des notifications
        $this->router->map('POST', '/v1/notifications/email', 'NotificationController#sendEmail');
        $this->router->map('GET', '/v1/notifications/templates', 'NotificationController#getTemplates');
    }
    
    public function dispatch() {
        // Mode debug
        if (isset($_GET['debug'])) {
            $this->debugRequest();
            return;
        }

        $match = $this->router->match();

        if ($match === false) {
            sendResponse(404, [
                'message' => 'Endpoint non trouvé',
                'request_uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'available_endpoints' => [
                    'GET /v1/test',
                    'GET /v1/health',
                    'GET /v1/locations',
                    'GET /v1/routes',
                    'GET /v1/trips',
                    'GET /v1/amenities'
                ]
            ]);
            return;
        }

        list($controller, $action) = explode('#', $match['target']);
        $params = $match['params'];

        $controllerFile = __DIR__ . "/../Controllers/{$controller}.php";
        if (!file_exists($controllerFile)) {
            sendResponse(500, ['message' => 'Contrôleur non trouvé: ' . $controller]);
            return;
        }

        require_once $controllerFile;

        if (!class_exists($controller)) {
            sendResponse(500, ['message' => 'Classe contrôleur non trouvée: ' . $controller]);
            return;
        }

        $controllerInstance = new $controller();

        if (!method_exists($controllerInstance, $action)) {
            sendResponse(500, ['message' => 'Méthode non trouvée: ' . $action]);
            return;
        }

        // Récupérer les données de la requête
        $requestBody = file_get_contents('php://input');
        $data = !empty($requestBody) ? json_decode($requestBody, true) : [];
        $queryParams = $_GET ?? [];

        // Fusionner les paramètres d'URL avec les données
        $allParams = array_merge($params, $data ?? [], $queryParams);

        try {
            // Appeler la méthode du contrôleur avec les paramètres
            $controllerInstance->$action($allParams);
        } catch (Exception $e) {
            error_log("Erreur dans {$controller}::{$action}: " . $e->getMessage());
            sendResponse(500, [
                'message' => 'Erreur interne du serveur',
                'error' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Une erreur est survenue'
            ]);
        }
    }

    private function debugRequest() {
        $debug = [
            'message' => 'Mode debug du routeur',
            'request_info' => [
                'uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'script_name' => $_SERVER['SCRIPT_NAME'],
                'path_info' => $_SERVER['PATH_INFO'] ?? 'N/A',
                'query_string' => $_SERVER['QUERY_STRING'] ?? 'N/A'
            ],
            'router_info' => [
                'base_path' => 'Configuré automatiquement',
                'current_path' => parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH)
            ],
            'available_endpoints' => [
                'GET /v1/test - Test de l\'API',
                'GET /v1/health - Vérification de santé',
                'GET /v1/locations - Liste des villes',
                'GET /v1/routes - Liste des trajets',
                'GET /v1/trips - Recherche de voyages',
                'GET /v1/amenities - Équipements des bus'
            ]
        ];

        sendResponse(200, $debug);
    }
}
