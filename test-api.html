<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - EasyBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #28a745;
        }
        .test-result.error {
            border-left-color: #dc3545;
        }
        .test-result.warning {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-5">Test de l'API EasyBus</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Tests des endpoints</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2 w-100" onclick="testEndpoint('locations', 'GET')">
                            Test GET /locations
                        </button>
                        <button class="btn btn-primary mb-2 w-100" onclick="testEndpoint('routes', 'GET')">
                            Test GET /routes
                        </button>
                        <button class="btn btn-primary mb-2 w-100" onclick="testEndpoint('trips', 'GET')">
                            Test GET /trips
                        </button>
                        <button class="btn btn-primary mb-2 w-100" onclick="testEndpoint('amenities', 'GET')">
                            Test GET /amenities
                        </button>
                        <button class="btn btn-warning mb-2 w-100" onclick="testAuth()">
                            Test Authentification
                        </button>
                        <button class="btn btn-success mb-2 w-100" onclick="runAllTests()">
                            Lancer tous les tests
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats des tests</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearResults()">
                            Effacer
                        </button>
                    </div>
                    <div class="card-body" id="testResults" style="max-height: 500px; overflow-y: auto;">
                        <p class="text-muted">Aucun test exécuté</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'api/v1';
        
        async function testEndpoint(endpoint, method = 'GET', data = null) {
            const startTime = Date.now();
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, options);
                const responseData = await response.json();
                const duration = Date.now() - startTime;
                
                addTestResult({
                    endpoint: `${method} /${endpoint}`,
                    status: response.status,
                    success: response.ok,
                    duration: duration,
                    data: responseData
                });
                
            } catch (error) {
                const duration = Date.now() - startTime;
                addTestResult({
                    endpoint: `${method} /${endpoint}`,
                    status: 'ERROR',
                    success: false,
                    duration: duration,
                    error: error.message
                });
            }
        }
        
        async function testAuth() {
            // Test d'inscription
            const testUser = {
                first_name: 'Test1',
                last_name: 'User1',
                email: '<EMAIL>', // Uniquement pour les tests
                phone: '+229123456789',
                password: '1234567'
            };
            
            await testEndpoint('auth/register', 'POST', testUser);
            
            // Test de connexion
            const loginData = {
                email: testUser.email,
                password: testUser.password
            };
            
            await testEndpoint('auth/login', 'POST', loginData);
        }
        
        async function runAllTests() {
            clearResults();
            addTestResult({
                endpoint: 'DÉBUT DES TESTS',
                status: 'INFO',
                success: true,
                duration: 0,
                data: { message: 'Lancement de tous les tests...' }
            });
            
            // Tests des endpoints publics
            await testEndpoint('locations', 'GET');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEndpoint('routes', 'GET');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEndpoint('trips', 'GET');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEndpoint('amenities', 'GET');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Test d'authentification
            await testAuth();
            
            addTestResult({
                endpoint: 'FIN DES TESTS',
                status: 'INFO',
                success: true,
                duration: 0,
                data: { message: 'Tous les tests terminés' }
            });
        }
        
        function addTestResult(result) {
            const container = document.getElementById('testResults');
            
            // Effacer le message par défaut
            if (container.innerHTML.includes('Aucun test exécuté')) {
                container.innerHTML = '';
            }
            
            const resultClass = result.success ? '' : 'error';
            const statusColor = result.success ? 'success' : 'danger';
            
            if (result.status === 'INFO') {
                statusColor = 'info';
            }
            
            const resultHtml = `
                <div class="test-result ${resultClass}">
                    <div class="d-flex justify-content-between align-items-start">
                        <strong>${result.endpoint}</strong>
                        <span class="badge bg-${statusColor}">${result.status}</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Durée: ${result.duration}ms</small>
                    </div>
                    ${result.error ? `
                        <div class="mt-2">
                            <strong class="text-danger">Erreur:</strong> ${result.error}
                        </div>
                    ` : ''}
                    ${result.data ? `
                        <div class="mt-2">
                            <details>
                                <summary class="text-muted" style="cursor: pointer;">Voir la réponse</summary>
                                <pre class="mt-2 p-2 bg-light rounded" style="font-size: 0.8rem; max-height: 200px; overflow-y: auto;">${JSON.stringify(result.data, null, 2)}</pre>
                            </details>
                        </div>
                    ` : ''}
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', resultHtml);
            container.scrollTop = container.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Aucun test exécuté</p>';
        }
        
        // Test automatique au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult({
                endpoint: 'SYSTÈME',
                status: 'INFO',
                success: true,
                duration: 0,
                data: {
                    message: 'Page de test chargée',
                    api_url: API_BASE_URL,
                    timestamp: new Date().toISOString()
                }
            });

            // Test automatique de base
            setTimeout(() => {
                testEndpoint('test', 'GET');
            }, 1000);
        });
    </script>
</body>
</html>
